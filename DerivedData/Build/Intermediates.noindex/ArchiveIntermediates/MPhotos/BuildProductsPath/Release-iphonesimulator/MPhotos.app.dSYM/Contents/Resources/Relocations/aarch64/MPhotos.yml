---
triple:          'arm64-apple-darwin'
binary-path:     "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/MPhotos"
relocations:
  - { offset: 0x9B3F0, size: 0x8, addend: 0x0, symName: '_$s7MPhotos11AppDelegateCMa', symObjAddr: 0x27C, symBinAddr: 0x10000169C, symSize: 0x20 }
  - { offset: 0x9B404, size: 0x8, addend: 0x0, symName: '_$sSo15UINavigationBarCMa', symObjAddr: 0x7F4, symBinAddr: 0x100001C14, symSize: 0x44 }
  - { offset: 0x9B418, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyas20_SwiftNewtypeWrapperSCSYWb', symObjAddr: 0x8A4, symBinAddr: 0x100001CC4, symSize: 0x2C }
  - { offset: 0x9B42C, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyas20_SwiftNewtypeWrapperSCs35_HasCustomAnyHashableRepresentationPWb', symObjAddr: 0x910, symBinAddr: 0x100001D30, symSize: 0x2C }
  - { offset: 0x9B440, size: 0x8, addend: 0x0, symName: '_$sSo29UIApplicationLaunchOptionsKeyas20_SwiftNewtypeWrapperSCSYWb', symObjAddr: 0x93C, symBinAddr: 0x100001D5C, symSize: 0x2C }
  - { offset: 0x9B454, size: 0x8, addend: 0x0, symName: '_$sSo29UIApplicationLaunchOptionsKeyas20_SwiftNewtypeWrapperSCs35_HasCustomAnyHashableRepresentationPWb', symObjAddr: 0x968, symBinAddr: 0x100001D88, symSize: 0x2C }
  - { offset: 0x9B468, size: 0x8, addend: 0x0, symName: '_$sSo29UIApplicationLaunchOptionsKeyaSHSCSQWb', symObjAddr: 0x994, symBinAddr: 0x100001DB4, symSize: 0x2C }
  - { offset: 0x9B47C, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyaSHSCSQWb', symObjAddr: 0x9C0, symBinAddr: 0x100001DE0, symSize: 0x2C }
  - { offset: 0x9B4D4, size: 0x8, addend: 0x0, symName: '_$sSo29UIApplicationLaunchOptionsKeyas21_ObjectiveCBridgeableSCsACP016_forceBridgeFromE1C_6resulty01_E5CTypeQz_xSgztFZTW', symObjAddr: 0x2BC, symBinAddr: 0x1000016DC, symSize: 0x14 }
  - { offset: 0x9B4F0, size: 0x8, addend: 0x0, symName: '_$sSo29UIApplicationLaunchOptionsKeyas21_ObjectiveCBridgeableSCsACP024_conditionallyBridgeFromE1C_6resultSb01_E5CTypeQz_xSgztFZTW', symObjAddr: 0x354, symBinAddr: 0x100001774, symSize: 0x18 }
  - { offset: 0x9B50C, size: 0x8, addend: 0x0, symName: '_$sSo29UIApplicationLaunchOptionsKeyas35_HasCustomAnyHashableRepresentationSCsACP03_tofgH0s0gH0VSgyFTW', symObjAddr: 0x5DC, symBinAddr: 0x1000019FC, symSize: 0x84 }
  - { offset: 0x9B528, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyas35_HasCustomAnyHashableRepresentationSCsACP03_toghI0s0hI0VSgyFTW', symObjAddr: 0x6A4, symBinAddr: 0x100001AC4, symSize: 0x84 }
  - { offset: 0x9B668, size: 0x8, addend: 0x0, symName: '_$s7MPhotos11AppDelegateC11application_29didFinishLaunchingWithOptionsSbSo13UIApplicationC_SDySo0j6LaunchI3KeyaypGSgtFTo', symObjAddr: 0x0, symBinAddr: 0x100001420, symSize: 0xA4 }
  - { offset: 0x9B6F2, size: 0x8, addend: 0x0, symName: '_$s7MPhotos11AppDelegateC11application_26configurationForConnecting7optionsSo20UISceneConfigurationCSo13UIApplicationC_So0I7SessionCSo0I17ConnectionOptionsCtFTo', symObjAddr: 0xA4, symBinAddr: 0x1000014C4, symSize: 0xE0 }
  - { offset: 0x9B78B, size: 0x8, addend: 0x0, symName: '_$s7MPhotos11AppDelegateC11application_23didDiscardSceneSessionsySo13UIApplicationC_ShySo14UISceneSessionCGtFTo', symObjAddr: 0x184, symBinAddr: 0x1000015A4, symSize: 0x4 }
  - { offset: 0x9B7B3, size: 0x8, addend: 0x0, symName: '_$s7MPhotos11AppDelegateCACycfcTo', symObjAddr: 0x188, symBinAddr: 0x1000015A8, symSize: 0x3C }
  - { offset: 0x9B7E8, size: 0x8, addend: 0x0, symName: '_$s7MPhotos11AppDelegateCfD', symObjAddr: 0x1C4, symBinAddr: 0x1000015E4, symSize: 0x30 }
  - { offset: 0x9B832, size: 0x8, addend: 0x0, symName: _main, symObjAddr: 0x1F4, symBinAddr: 0x100001614, symSize: 0x88 }
  - { offset: 0x9B868, size: 0x8, addend: 0x0, symName: '_$sSo29UIApplicationLaunchOptionsKeyaSYSCSY8rawValue03RawF0QzvgTW', symObjAddr: 0x5B4, symBinAddr: 0x1000019D4, symSize: 0x28 }
  - { offset: 0x9B8C1, size: 0x8, addend: 0x0, symName: '_$s7MPhotos11AppDelegateC11application_29didFinishLaunchingWithOptionsSbSo13UIApplicationC_SDySo0j6LaunchI3KeyaypGSgtFTf4ddd_n', symObjAddr: 0x728, symBinAddr: 0x100001B48, symSize: 0xB8 }
  - { offset: 0x9B9FF, size: 0x8, addend: 0x0, symName: '_$s7MPhotos16PhotoDataManagerC6shared_WZ', symObjAddr: 0x0, symBinAddr: 0x100001E34, symSize: 0x54 }
  - { offset: 0x9BA23, size: 0x8, addend: 0x0, symName: '_$s7MPhotos16PhotoDataManagerC6sharedACvpZ', symObjAddr: 0x5018, symBinAddr: 0x100011F90, symSize: 0x0 }
  - { offset: 0x9BB78, size: 0x8, addend: 0x0, symName: '_$s7MPhotos16PhotoDataManagerC6shared_WZ', symObjAddr: 0x0, symBinAddr: 0x100001E34, symSize: 0x54 }
  - { offset: 0x9BC19, size: 0x8, addend: 0x0, symName: '_$sIeg_IeyB_TR', symObjAddr: 0x480, symBinAddr: 0x1000022B4, symSize: 0x2C }
  - { offset: 0x9BC31, size: 0x8, addend: 0x0, symName: '_$s7MPhotos16PhotoDataManagerCMa', symObjAddr: 0x4D8, symBinAddr: 0x10000230C, symSize: 0x20 }
  - { offset: 0x9BC45, size: 0x8, addend: 0x0, symName: '_$s7MPhotos16PhotoDataManagerC16requestThumbnail3for10targetSize10completions5Int32VSo7PHAssetC_So6CGSizeVySo7UIImageCSgctFyAP_SDys11AnyHashableVypGSgtcfU_yyScMYccfU_TA', symObjAddr: 0x524, symBinAddr: 0x100002358, symSize: 0x28 }
  - { offset: 0x9BC81, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x54C, symBinAddr: 0x100002380, symSize: 0x10 }
  - { offset: 0x9BC95, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x55C, symBinAddr: 0x100002390, symSize: 0x8 }
  - { offset: 0x9BCA9, size: 0x8, addend: 0x0, symName: '_$s8Dispatch0A13WorkItemFlagsVACs10SetAlgebraAAWl', symObjAddr: 0x564, symBinAddr: 0x100002398, symSize: 0x44 }
  - { offset: 0x9BCBD, size: 0x8, addend: 0x0, symName: ___swift_instantiateConcreteTypeFromMangledName, symObjAddr: 0x5A8, symBinAddr: 0x1000023DC, symSize: 0x44 }
  - { offset: 0x9BCD1, size: 0x8, addend: 0x0, symName: '_$sSay8Dispatch0A13WorkItemFlagsVGSayxGSTsWl', symObjAddr: 0x5EC, symBinAddr: 0x100002420, symSize: 0x48 }
  - { offset: 0x9BCE5, size: 0x8, addend: 0x0, symName: ___swift_instantiateConcreteTypeFromMangledNameAbstract, symObjAddr: 0x634, symBinAddr: 0x100002468, symSize: 0x48 }
  - { offset: 0x9BEE0, size: 0x8, addend: 0x0, symName: '_$s7MPhotos16PhotoDataManagerC14fetchAllPhotosSo13PHFetchResultCySo7PHAssetCGyF', symObjAddr: 0x54, symBinAddr: 0x100001E88, symSize: 0x164 }
  - { offset: 0x9C01A, size: 0x8, addend: 0x0, symName: '_$s7MPhotos16PhotoDataManagerC03getB10StatisticsAA0bF0VyF', symObjAddr: 0x1B8, symBinAddr: 0x100001FEC, symSize: 0xF4 }
  - { offset: 0x9C173, size: 0x8, addend: 0x0, symName: '_$s7MPhotos16PhotoDataManagerC16requestThumbnail3for10targetSize10completions5Int32VSo7PHAssetC_So6CGSizeVySo7UIImageCSgctFyAP_SDys11AnyHashableVypGSgtcfU_', symObjAddr: 0x2AC, symBinAddr: 0x1000020E0, symSize: 0x1D4 }
  - { offset: 0x9C203, size: 0x8, addend: 0x0, symName: '_$s7MPhotos16PhotoDataManagerCfD', symObjAddr: 0x4AC, symBinAddr: 0x1000022E0, symSize: 0x2C }
  - { offset: 0x9C27E, size: 0x8, addend: 0x0, symName: '_$s7MPhotos16PhotoDataManagerC11fetchAssets4withSo13PHFetchResultCySo7PHAssetCGSo0J9MediaTypeV_tFTf4nd_n', symObjAddr: 0x67C, symBinAddr: 0x1000024B0, symSize: 0x158 }
  - { offset: 0x9C449, size: 0x8, addend: 0x0, symName: '_$s7MPhotos20MainTabBarControllerC11viewDidLoadyyFTo', symObjAddr: 0x0, symBinAddr: 0x100002834, symSize: 0x5C }
  - { offset: 0x9C613, size: 0x8, addend: 0x0, symName: '_$s7MPhotos20MainTabBarControllerCMa', symObjAddr: 0x558, symBinAddr: 0x100002D8C, symSize: 0x20 }
  - { offset: 0x9C673, size: 0x8, addend: 0x0, symName: '_$s7MPhotos20MainTabBarControllerC11viewDidLoadyyFTo', symObjAddr: 0x0, symBinAddr: 0x100002834, symSize: 0x5C }
  - { offset: 0x9C8FB, size: 0x8, addend: 0x0, symName: '_$s7MPhotos20MainTabBarControllerC20setupViewControllers33_4B23E2A9500355D8539B9FC3D9E272D8LLyyF', symObjAddr: 0x5C, symBinAddr: 0x100002890, symSize: 0x21C }
  - { offset: 0x9CAB7, size: 0x8, addend: 0x0, symName: '_$s7MPhotos20MainTabBarControllerC15setupAppearance33_4B23E2A9500355D8539B9FC3D9E272D8LLyyF', symObjAddr: 0x278, symBinAddr: 0x100002AAC, symSize: 0x108 }
  - { offset: 0x9CB77, size: 0x8, addend: 0x0, symName: '_$s7MPhotos20MainTabBarControllerC4tabsACSaySo5UITabCG_tcfcTo', symObjAddr: 0x380, symBinAddr: 0x100002BB4, symSize: 0x98 }
  - { offset: 0x9CBCF, size: 0x8, addend: 0x0, symName: '_$s7MPhotos20MainTabBarControllerC7nibName6bundleACSSSg_So8NSBundleCSgtcfcTo', symObjAddr: 0x418, symBinAddr: 0x100002C4C, symSize: 0x9C }
  - { offset: 0x9CC29, size: 0x8, addend: 0x0, symName: '_$s7MPhotos20MainTabBarControllerC5coderACSgSo7NSCoderC_tcfcTo', symObjAddr: 0x4B4, symBinAddr: 0x100002CE8, symSize: 0x74 }
  - { offset: 0x9CC60, size: 0x8, addend: 0x0, symName: '_$s7MPhotos20MainTabBarControllerCfD', symObjAddr: 0x528, symBinAddr: 0x100002D5C, symSize: 0x30 }
  - { offset: 0x9CCAB, size: 0x8, addend: 0x0, symName: '_$s7MPhotos20MainTabBarControllerC09createNavE033_4B23E2A9500355D8539B9FC3D9E272D8LL6rootVC5title9imageNameSo012UINavigationE0CSo06UIViewE0C_S2StFTf4nnnd_n', symObjAddr: 0x578, symBinAddr: 0x100002DAC, symSize: 0x1B8 }
  - { offset: 0x9CD7F, size: 0x8, addend: 0x0, symName: '_$s7MPhotos20MainTabBarControllerC19createPlaceholderVC33_4B23E2A9500355D8539B9FC3D9E272D8LL5titleSo06UIViewE0CSS_tFTf4nd_n', symObjAddr: 0x730, symBinAddr: 0x100002F64, symSize: 0x374 }
  - { offset: 0x9CFE0, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateC6windowSo8UIWindowCSgvgTo', symObjAddr: 0x0, symBinAddr: 0x1000032D8, symSize: 0x10 }
  - { offset: 0x9D1FA, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateCfETo', symObjAddr: 0x264, symBinAddr: 0x10000353C, symSize: 0x10 }
  - { offset: 0x9D229, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateCMa', symObjAddr: 0x274, symBinAddr: 0x10000354C, symSize: 0x20 }
  - { offset: 0x9D23D, size: 0x8, addend: 0x0, symName: '_$sSo17OS_dispatch_queueCMa', symObjAddr: 0x578, symBinAddr: 0x100003850, symSize: 0x44 }
  - { offset: 0x9D251, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateC5scene_13willConnectTo7optionsySo7UISceneC_So0I7SessionCSo0I17ConnectionOptionsCtFyyScMYccfU_TA', symObjAddr: 0x5E8, symBinAddr: 0x1000038C0, symSize: 0x8 }
  - { offset: 0x9D265, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x5F0, symBinAddr: 0x1000038C8, symSize: 0x10 }
  - { offset: 0x9D279, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x600, symBinAddr: 0x1000038D8, symSize: 0x8 }
  - { offset: 0x9D3A3, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateC6windowSo8UIWindowCSgvgTo', symObjAddr: 0x0, symBinAddr: 0x1000032D8, symSize: 0x10 }
  - { offset: 0x9D3BB, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateC6windowSo8UIWindowCSgvgTo', symObjAddr: 0x0, symBinAddr: 0x1000032D8, symSize: 0x10 }
  - { offset: 0x9D403, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateC6windowSo8UIWindowCSgvsTo', symObjAddr: 0x10, symBinAddr: 0x1000032E8, symSize: 0x34 }
  - { offset: 0x9D463, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateC5scene_13willConnectTo7optionsySo7UISceneC_So0I7SessionCSo0I17ConnectionOptionsCtFyyScMYccfU_', symObjAddr: 0x44, symBinAddr: 0x10000331C, symSize: 0x38 }
  - { offset: 0x9D4C9, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateC5scene_13willConnectTo7optionsySo7UISceneC_So0I7SessionCSo0I17ConnectionOptionsCtFTo', symObjAddr: 0x7C, symBinAddr: 0x100003354, symSize: 0x7C }
  - { offset: 0x9D4F3, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateC18sceneDidDisconnectyySo7UISceneCFTo', symObjAddr: 0xF8, symBinAddr: 0x1000033D0, symSize: 0x4 }
  - { offset: 0x9D507, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateC20sceneDidBecomeActiveyySo7UISceneCFTo', symObjAddr: 0xFC, symBinAddr: 0x1000033D4, symSize: 0x4 }
  - { offset: 0x9D51B, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateC21sceneWillResignActiveyySo7UISceneCFTo', symObjAddr: 0x100, symBinAddr: 0x1000033D8, symSize: 0x4 }
  - { offset: 0x9D52F, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateC24sceneWillEnterForegroundyySo7UISceneCFTo', symObjAddr: 0x104, symBinAddr: 0x1000033DC, symSize: 0x4 }
  - { offset: 0x9D543, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateC23sceneDidEnterBackgroundyySo7UISceneCFTo', symObjAddr: 0x108, symBinAddr: 0x1000033E0, symSize: 0x4 }
  - { offset: 0x9D563, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateC27checkPhotoLibraryPermission33_2145FA1267F93B6F4563514BDBB8947ELL4fromySo16UIViewControllerC_tFyAA0G7ManagerC0G6StatusOcfU_', symObjAddr: 0x10C, symBinAddr: 0x1000033E4, symSize: 0xE0 }
  - { offset: 0x9D603, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateCACycfcTo', symObjAddr: 0x1EC, symBinAddr: 0x1000034C4, symSize: 0x48 }
  - { offset: 0x9D638, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateCfD', symObjAddr: 0x234, symBinAddr: 0x10000350C, symSize: 0x30 }
  - { offset: 0x9D6B8, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateC5scene_13willConnectTo7optionsySo7UISceneC_So0I7SessionCSo0I17ConnectionOptionsCtFTf4nddn_n', symObjAddr: 0x294, symBinAddr: 0x10000356C, symSize: 0x2E4 }
  - { offset: 0x9D905, size: 0x8, addend: 0x0, symName: '_$s7MPhotos17PermissionManagerC6sharedACvpZ', symObjAddr: 0x8890, symBinAddr: 0x100011F98, symSize: 0x0 }
  - { offset: 0x9DA95, size: 0x8, addend: 0x0, symName: '_$s7MPhotos17PermissionManagerC05checkB11OnAppLaunch4from10completionySo16UIViewControllerC_yAC0B6StatusOctF0102$s7MPhotos13SceneDelegateC27checkPhotoLibraryPermission33_2145FA1267F93B6F4563514BDBB8947ELL4fromySo16jk10C_tFyAA0G7c4C0G6L5OcfU_Tf1ncn_n', symObjAddr: 0x0, symBinAddr: 0x1000038E0, symSize: 0x458 }
  - { offset: 0x9DE81, size: 0x8, addend: 0x0, symName: '_$s7MPhotos17PermissionManagerC6shared_WZ', symObjAddr: 0x458, symBinAddr: 0x100003D38, symSize: 0x28 }
  - { offset: 0x9DEB1, size: 0x8, addend: 0x0, symName: '_$sSo21PHAuthorizationStatusVIegy_ABIeyBy_TR', symObjAddr: 0x6EC, symBinAddr: 0x100003FCC, symSize: 0x3C }
  - { offset: 0x9DEC9, size: 0x8, addend: 0x0, symName: '_$sSo13UIAlertActionCIegg_ABIeyBy_TR', symObjAddr: 0x928, symBinAddr: 0x100004208, symSize: 0x50 }
  - { offset: 0x9DEE1, size: 0x8, addend: 0x0, symName: '_$s7MPhotos17PermissionManagerCMa', symObjAddr: 0xB94, symBinAddr: 0x100004474, symSize: 0x20 }
  - { offset: 0x9DEF5, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0xBB4, symBinAddr: 0x100004494, symSize: 0x10 }
  - { offset: 0x9DF09, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0xBC4, symBinAddr: 0x1000044A4, symSize: 0x8 }
  - { offset: 0x9DF1D, size: 0x8, addend: 0x0, symName: '_$s7MPhotos17PermissionManagerC05checkB11OnAppLaunch4from10completionySo16UIViewControllerC_yAC0B6StatusOctFyAJcfU_0102$s7MPhotos13SceneDelegateC27checkPhotoLibraryPermission33_2145FA1267F93B6F4563514BDBB8947ELL4fromySo16jk10C_tFyAA0G7c4C0G6L5OcfU_Tf1nnnc_n', symObjAddr: 0xBCC, symBinAddr: 0x1000044AC, symSize: 0xC0 }
  - { offset: 0x9DF62, size: 0x8, addend: 0x0, symName: '_$s7MPhotos17PermissionManagerC05checkB11OnAppLaunch4from10completionySo16UIViewControllerC_yAC0B6StatusOctFyAJcfU_', symObjAddr: 0xC8C, symBinAddr: 0x10000456C, symSize: 0xD4 }
  - { offset: 0x9DFA6, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4findys10_HashTableV6BucketV6bucket_Sb5foundtxSHRzlFSo38UIApplicationOpenExternalURLOptionsKeya_Tg5', symObjAddr: 0xD60, symBinAddr: 0x100004640, symSize: 0x80 }
  - { offset: 0x9E045, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4find_9hashValues10_HashTableV6BucketV6bucket_Sb5foundtx_SitSHRzlFSo38UIApplicationOpenExternalURLOptionsKeya_Tg5', symObjAddr: 0xDE0, symBinAddr: 0x1000046C0, symSize: 0x178 }
  - { offset: 0x9E198, size: 0x8, addend: 0x0, symName: '_$s10Foundation3URLVSgWOh', symObjAddr: 0x127C, symBinAddr: 0x100004B18, symSize: 0x40 }
  - { offset: 0x9E1AC, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeya_yptWOc', symObjAddr: 0x12BC, symBinAddr: 0x100004B58, symSize: 0x48 }
  - { offset: 0x9E1C0, size: 0x8, addend: 0x0, symName: '_$sypWOb', symObjAddr: 0x1304, symBinAddr: 0x100004BA0, symSize: 0x10 }
  - { offset: 0x9E1D4, size: 0x8, addend: 0x0, symName: '_$s7MPhotos17PermissionManagerC04showB11DeniedAlert4fromySo16UIViewControllerC_tFySo13UIAlertActionCcfU_TA', symObjAddr: 0x1314, symBinAddr: 0x100004BB0, symSize: 0x4 }
  - { offset: 0x9E1EC, size: 0x8, addend: 0x0, symName: '_$s7MPhotos17PermissionManagerC04showB11DeniedAlert4fromySo16UIViewControllerC_tFySo13UIAlertActionCcfU_TA', symObjAddr: 0x1314, symBinAddr: 0x100004BB0, symSize: 0x4 }
  - { offset: 0x9E200, size: 0x8, addend: 0x0, symName: '_$s7MPhotos17PermissionManagerC04showB11DeniedAlert4fromySo16UIViewControllerC_tFySo13UIAlertActionCcfU_TA', symObjAddr: 0x1314, symBinAddr: 0x100004BB0, symSize: 0x4 }
  - { offset: 0x9E213, size: 0x8, addend: 0x0, symName: '_$s7MPhotos17PermissionManagerC05checkB11OnAppLaunch4from10completionySo16UIViewControllerC_yAC0B6StatusOctFyAJcfU_TA', symObjAddr: 0x1370, symBinAddr: 0x100004C0C, symSize: 0xC }
  - { offset: 0x9E227, size: 0x8, addend: 0x0, symName: '_$s7MPhotos17PermissionManagerC019requestPhotoLibraryB010completionyyAC0B6StatusOc_tFySo015PHAuthorizationH0VcfU_TA', symObjAddr: 0x13A8, symBinAddr: 0x100004C44, symSize: 0xC }
  - { offset: 0x9E23B, size: 0x8, addend: 0x0, symName: '_$s7MPhotos17PermissionManagerC019requestPhotoLibraryB010completionyyAC0B6StatusOc_tFySo015PHAuthorizationH0VcfU_yyScMYccfU_TA', symObjAddr: 0x1424, symBinAddr: 0x100004C7C, symSize: 0xC }
  - { offset: 0x9E378, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSo38UIApplicationOpenExternalURLOptionsKeya_ypTt0g5Tf4g_n', symObjAddr: 0xF58, symBinAddr: 0x100004838, symSize: 0xF0 }
  - { offset: 0x9E626, size: 0x8, addend: 0x0, symName: '_$s7MPhotos17PermissionManagerC019requestPhotoLibraryB010completionyyAC0B6StatusOc_tFySo015PHAuthorizationH0VcfU_', symObjAddr: 0x480, symBinAddr: 0x100003D60, symSize: 0x1E4 }
  - { offset: 0x9E685, size: 0x8, addend: 0x0, symName: '_$s7MPhotos17PermissionManagerC019requestPhotoLibraryB010completionyyAC0B6StatusOc_tFySo015PHAuthorizationH0VcfU_yyScMYccfU_', symObjAddr: 0x664, symBinAddr: 0x100003F44, symSize: 0x88 }
  - { offset: 0x9E71D, size: 0x8, addend: 0x0, symName: '_$s7MPhotos17PermissionManagerC04showB11DeniedAlert4fromySo16UIViewControllerC_tF', symObjAddr: 0x728, symBinAddr: 0x100004008, symSize: 0x200 }
  - { offset: 0x9E7D8, size: 0x8, addend: 0x0, symName: '_$s7MPhotos17PermissionManagerC22showLimitedAccessAlert4fromySo16UIViewControllerC_tF', symObjAddr: 0x978, symBinAddr: 0x100004258, symSize: 0x20C }
  - { offset: 0x9E86D, size: 0x8, addend: 0x0, symName: '_$s7MPhotos17PermissionManagerCfD', symObjAddr: 0xB84, symBinAddr: 0x100004464, symSize: 0x10 }
  - { offset: 0x9E8C4, size: 0x8, addend: 0x0, symName: '_$s7MPhotos17PermissionManagerC15openAppSettings33_7A20A667FEBED227F44F8689CCAF6561LLyyFTf4d_n', symObjAddr: 0x1048, symBinAddr: 0x100004928, symSize: 0x1F0 }
  - { offset: 0x9EA03, size: 0x8, addend: 0x0, symName: '_$sSo7UIImageCSgSDys11AnyHashableVypGSgIeggg_ACSo12NSDictionaryCSgIeyByy_TR', symObjAddr: 0x0, symBinAddr: 0x100004CA4, symSize: 0x94 }
  - { offset: 0x9EA1B, size: 0x8, addend: 0x0, symName: '_$sSo7UIImageCSgSDys11AnyHashableVypGSgIeggg_ACSo12NSDictionaryCSgIeyByy_TR', symObjAddr: 0x0, symBinAddr: 0x100004CA4, symSize: 0x94 }
  - { offset: 0x9EC96, size: 0x8, addend: 0x0, symName: '_$s7MPhotos27PhotoGridTestViewControllerCfETo', symObjAddr: 0x22AC, symBinAddr: 0x100006F24, symSize: 0x98 }
  - { offset: 0x9ECC5, size: 0x8, addend: 0x0, symName: '_$s7MPhotos27PhotoGridTestViewControllerCMa', symObjAddr: 0x2344, symBinAddr: 0x100006FBC, symSize: 0x20 }
  - { offset: 0x9ECD9, size: 0x8, addend: 0x0, symName: '_$s7MPhotos27PhotoGridTestViewControllerC04testB7Loading33_1F57B767E7DC0F6E44807B7E3A1ABA97LLyyFySo7UIImageCSgcfU_TA', symObjAddr: 0x23C4, symBinAddr: 0x10000703C, symSize: 0xC }
  - { offset: 0x9ECED, size: 0x8, addend: 0x0, symName: '_$s7MPhotos16PhotoDataManagerC16requestThumbnail3for10targetSize10completions5Int32VSo7PHAssetC_So6CGSizeVySo7UIImageCSgctFyAP_SDys11AnyHashableVypGSgtcfU_TA', symObjAddr: 0x23F4, symBinAddr: 0x10000706C, symSize: 0x8 }
  - { offset: 0x9ED01, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x23FC, symBinAddr: 0x100007074, symSize: 0x10 }
  - { offset: 0x9ED15, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x240C, symBinAddr: 0x100007084, symSize: 0x8 }
  - { offset: 0x9ED29, size: 0x8, addend: 0x0, symName: '_$s7MPhotos27PhotoGridTestViewControllerC04loadB4Data33_1F57B767E7DC0F6E44807B7E3A1ABA97LLyyFyyYbcfU_TA', symObjAddr: 0x2414, symBinAddr: 0x10000708C, symSize: 0x8 }
  - { offset: 0x9ED3D, size: 0x8, addend: 0x0, symName: '_$s7MPhotos27PhotoGridTestViewControllerC04loadB4Data33_1F57B767E7DC0F6E44807B7E3A1ABA97LLyyFyyYbcfU_yyScMYccfU_TA', symObjAddr: 0x2558, symBinAddr: 0x1000070B8, symSize: 0xC }
  - { offset: 0x9ED71, size: 0x8, addend: 0x0, symName: '_$s7MPhotos27PhotoGridTestViewControllerC11viewDidLoadyyFTo', symObjAddr: 0x94, symBinAddr: 0x100004D38, symSize: 0x60 }
  - { offset: 0x9EF1A, size: 0x8, addend: 0x0, symName: '_$s7MPhotos27PhotoGridTestViewControllerC7setupUI33_1F57B767E7DC0F6E44807B7E3A1ABA97LLyyF', symObjAddr: 0xF4, symBinAddr: 0x100004D98, symSize: 0x68C }
  - { offset: 0x9EF9B, size: 0x8, addend: 0x0, symName: '_$s7MPhotos27PhotoGridTestViewControllerC16setupConstraints33_1F57B767E7DC0F6E44807B7E3A1ABA97LLyyF', symObjAddr: 0x780, symBinAddr: 0x100005424, symSize: 0x6E0 }
  - { offset: 0x9F0B9, size: 0x8, addend: 0x0, symName: '_$s7MPhotos27PhotoGridTestViewControllerC26checkPermissionAndLoadData33_1F57B767E7DC0F6E44807B7E3A1ABA97LLyyF', symObjAddr: 0xE60, symBinAddr: 0x100005B04, symSize: 0x198 }
  - { offset: 0x9F193, size: 0x8, addend: 0x0, symName: '_$s7MPhotos27PhotoGridTestViewControllerC04loadB4Data33_1F57B767E7DC0F6E44807B7E3A1ABA97LLyyF', symObjAddr: 0xFF8, symBinAddr: 0x100005C9C, symSize: 0x274 }
  - { offset: 0x9F1F6, size: 0x8, addend: 0x0, symName: '_$s7MPhotos27PhotoGridTestViewControllerC04loadB4Data33_1F57B767E7DC0F6E44807B7E3A1ABA97LLyyFyyYbcfU_', symObjAddr: 0x126C, symBinAddr: 0x100005F10, symSize: 0x274 }
  - { offset: 0x9F291, size: 0x8, addend: 0x0, symName: '_$s7MPhotos27PhotoGridTestViewControllerC04loadB4Data33_1F57B767E7DC0F6E44807B7E3A1ABA97LLyyFyyYbcfU_yyScMYccfU_', symObjAddr: 0x14E0, symBinAddr: 0x100006184, symSize: 0x58 }
  - { offset: 0x9F309, size: 0x8, addend: 0x0, symName: '_$s7MPhotos27PhotoGridTestViewControllerC8updateUI33_1F57B767E7DC0F6E44807B7E3A1ABA97LL4withyAA0B10StatisticsV_tF', symObjAddr: 0x1564, symBinAddr: 0x1000061DC, symSize: 0x2B4 }
  - { offset: 0x9F660, size: 0x8, addend: 0x0, symName: '_$s7MPhotos27PhotoGridTestViewControllerC04testB7Loading33_1F57B767E7DC0F6E44807B7E3A1ABA97LLyyF', symObjAddr: 0x1818, symBinAddr: 0x100006490, symSize: 0x48C }
  - { offset: 0x9F848, size: 0x8, addend: 0x0, symName: '_$s7MPhotos27PhotoGridTestViewControllerC04testB7Loading33_1F57B767E7DC0F6E44807B7E3A1ABA97LLyyFySo7UIImageCSgcfU_', symObjAddr: 0x1CA4, symBinAddr: 0x10000691C, symSize: 0x1BC }
  - { offset: 0x9F936, size: 0x8, addend: 0x0, symName: '_$s7MPhotos27PhotoGridTestViewControllerC04testB7Loading33_1F57B767E7DC0F6E44807B7E3A1ABA97LLyyFTo', symObjAddr: 0x1E60, symBinAddr: 0x100006AD8, symSize: 0x34 }
  - { offset: 0x9F95E, size: 0x8, addend: 0x0, symName: '_$s7MPhotos27PhotoGridTestViewControllerC11refreshData33_1F57B767E7DC0F6E44807B7E3A1ABA97LLyyFTo', symObjAddr: 0x1E94, symBinAddr: 0x100006B0C, symSize: 0x34 }
  - { offset: 0x9F9DF, size: 0x8, addend: 0x0, symName: '_$s7MPhotos27PhotoGridTestViewControllerC7nibName6bundleACSSSg_So8NSBundleCSgtcfc', symObjAddr: 0x1EC8, symBinAddr: 0x100006B40, symSize: 0x1B0 }
  - { offset: 0x9FB19, size: 0x8, addend: 0x0, symName: '_$s7MPhotos27PhotoGridTestViewControllerC7nibName6bundleACSSSg_So8NSBundleCSgtcfcTo', symObjAddr: 0x2078, symBinAddr: 0x100006CF0, symSize: 0x5C }
  - { offset: 0x9FB45, size: 0x8, addend: 0x0, symName: '_$s7MPhotos27PhotoGridTestViewControllerC5coderACSgSo7NSCoderC_tcfc', symObjAddr: 0x20D4, symBinAddr: 0x100006D4C, symSize: 0x184 }
  - { offset: 0x9FC71, size: 0x8, addend: 0x0, symName: '_$s7MPhotos27PhotoGridTestViewControllerC5coderACSgSo7NSCoderC_tcfcTo', symObjAddr: 0x2258, symBinAddr: 0x100006ED0, symSize: 0x24 }
  - { offset: 0x9FC85, size: 0x8, addend: 0x0, symName: '_$s7MPhotos27PhotoGridTestViewControllerCfD', symObjAddr: 0x227C, symBinAddr: 0x100006EF4, symSize: 0x30 }
  - { offset: 0x9FDDD, size: 0x8, addend: 0x0, symName: '_$s7MPhotos19ResourceBundleClass33_E00857F5A182E2036586C1283CBC8150LLCMa', symObjAddr: 0x10, symBinAddr: 0x1000070D4, symSize: 0x20 }
...
