---
triple:          'arm64-apple-darwin'
binary-path:     "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/MPhotos"
relocations:
  - { offset: 0x987CB, size: 0x8, addend: 0x0, symName: '_$sIeg_IeyB_TR', symObjAddr: 0x7C, symBinAddr: 0x10000144C, symSize: 0x2C }
  - { offset: 0x98818, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateCfETo', symObjAddr: 0x290, symBinAddr: 0x100001660, symSize: 0x10 }
  - { offset: 0x98847, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateCMa', symObjAddr: 0x2A0, symBinAddr: 0x100001670, symSize: 0x20 }
  - { offset: 0x9885B, size: 0x8, addend: 0x0, symName: '_$sSo17OS_dispatch_queueCMa', symObjAddr: 0xA48, symBinAddr: 0x100001E18, symSize: 0x44 }
  - { offset: 0x9886F, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateC5scene_13willConnectTo7optionsySo7UISceneC_So0I7SessionCSo0I17ConnectionOptionsCtFyyScMYccfU_TA', symObjAddr: 0xAB8, symBinAddr: 0x100001E88, symSize: 0x8 }
  - { offset: 0x98883, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0xAC0, symBinAddr: 0x100001E90, symSize: 0x10 }
  - { offset: 0x98897, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0xAD0, symBinAddr: 0x100001EA0, symSize: 0x8 }
  - { offset: 0x988AB, size: 0x8, addend: 0x0, symName: ___swift_instantiateConcreteTypeFromMangledName, symObjAddr: 0xAD8, symBinAddr: 0x100001EA8, symSize: 0x44 }
  - { offset: 0x988BF, size: 0x8, addend: 0x0, symName: '_$sSay8Dispatch0A13WorkItemFlagsVGSayxGSTsWl', symObjAddr: 0xB1C, symBinAddr: 0x100001EEC, symSize: 0x48 }
  - { offset: 0x988D3, size: 0x8, addend: 0x0, symName: ___swift_instantiateConcreteTypeFromMangledNameAbstract, symObjAddr: 0xB64, symBinAddr: 0x100001F34, symSize: 0x48 }
  - { offset: 0x988E7, size: 0x8, addend: 0x0, symName: '_$sSo29UIApplicationLaunchOptionsKeyas20_SwiftNewtypeWrapperSCSYWb', symObjAddr: 0xC2C, symBinAddr: 0x100001FFC, symSize: 0x2C }
  - { offset: 0x988FB, size: 0x8, addend: 0x0, symName: '_$sSo29UIApplicationLaunchOptionsKeyas20_SwiftNewtypeWrapperSCs35_HasCustomAnyHashableRepresentationPWb', symObjAddr: 0xC98, symBinAddr: 0x100002068, symSize: 0x2C }
  - { offset: 0x9890F, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyas20_SwiftNewtypeWrapperSCSYWb', symObjAddr: 0xCC4, symBinAddr: 0x100002094, symSize: 0x2C }
  - { offset: 0x98923, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyas20_SwiftNewtypeWrapperSCs35_HasCustomAnyHashableRepresentationPWb', symObjAddr: 0xCF0, symBinAddr: 0x1000020C0, symSize: 0x2C }
  - { offset: 0x98937, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyaSHSCSQWb', symObjAddr: 0xD1C, symBinAddr: 0x1000020EC, symSize: 0x2C }
  - { offset: 0x9894B, size: 0x8, addend: 0x0, symName: '_$sSo29UIApplicationLaunchOptionsKeyaSHSCSQWb', symObjAddr: 0xD48, symBinAddr: 0x100002118, symSize: 0x2C }
  - { offset: 0x989B4, size: 0x8, addend: 0x0, symName: '_$sSo29UIApplicationLaunchOptionsKeyas21_ObjectiveCBridgeableSCsACP016_forceBridgeFromE1C_6resulty01_E5CTypeQz_xSgztFZTW', symObjAddr: 0x4DC, symBinAddr: 0x1000018AC, symSize: 0x14 }
  - { offset: 0x989D0, size: 0x8, addend: 0x0, symName: '_$sSo29UIApplicationLaunchOptionsKeyas21_ObjectiveCBridgeableSCsACP024_conditionallyBridgeFromE1C_6resultSb01_E5CTypeQz_xSgztFZTW', symObjAddr: 0x4F0, symBinAddr: 0x1000018C0, symSize: 0x18 }
  - { offset: 0x989EC, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyas35_HasCustomAnyHashableRepresentationSCsACP03_toghI0s0hI0VSgyFTW', symObjAddr: 0x5D4, symBinAddr: 0x1000019A4, symSize: 0x84 }
  - { offset: 0x98A08, size: 0x8, addend: 0x0, symName: '_$sSo29UIApplicationLaunchOptionsKeyas35_HasCustomAnyHashableRepresentationSCsACP03_tofgH0s0gH0VSgyFTW', symObjAddr: 0x6C8, symBinAddr: 0x100001A98, symSize: 0x84 }
  - { offset: 0x98B5B, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateC6windowSo8UIWindowCSgvgTo', symObjAddr: 0x0, symBinAddr: 0x1000013D0, symSize: 0x10 }
  - { offset: 0x98B73, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateC6windowSo8UIWindowCSgvgTo', symObjAddr: 0x0, symBinAddr: 0x1000013D0, symSize: 0x10 }
  - { offset: 0x98BBB, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateC6windowSo8UIWindowCSgvsTo', symObjAddr: 0x10, symBinAddr: 0x1000013E0, symSize: 0x34 }
  - { offset: 0x98C1B, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateC5scene_13willConnectTo7optionsySo7UISceneC_So0I7SessionCSo0I17ConnectionOptionsCtFyyScMYccfU_', symObjAddr: 0x44, symBinAddr: 0x100001414, symSize: 0x38 }
  - { offset: 0x98C81, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateC5scene_13willConnectTo7optionsySo7UISceneC_So0I7SessionCSo0I17ConnectionOptionsCtFTo', symObjAddr: 0xA8, symBinAddr: 0x100001478, symSize: 0x7C }
  - { offset: 0x98CAB, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateC18sceneDidDisconnectyySo7UISceneCFTo', symObjAddr: 0x124, symBinAddr: 0x1000014F4, symSize: 0x4 }
  - { offset: 0x98CBF, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateC20sceneDidBecomeActiveyySo7UISceneCFTo', symObjAddr: 0x128, symBinAddr: 0x1000014F8, symSize: 0x4 }
  - { offset: 0x98CD3, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateC21sceneWillResignActiveyySo7UISceneCFTo', symObjAddr: 0x12C, symBinAddr: 0x1000014FC, symSize: 0x4 }
  - { offset: 0x98CE7, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateC24sceneWillEnterForegroundyySo7UISceneCFTo', symObjAddr: 0x130, symBinAddr: 0x100001500, symSize: 0x4 }
  - { offset: 0x98CFB, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateC23sceneDidEnterBackgroundyySo7UISceneCFTo', symObjAddr: 0x134, symBinAddr: 0x100001504, symSize: 0x4 }
  - { offset: 0x98D1B, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateC27checkPhotoLibraryPermission33_2145FA1267F93B6F4563514BDBB8947ELL4fromySo16UIViewControllerC_tFyAA0G7ManagerC0G6StatusOcfU_', symObjAddr: 0x138, symBinAddr: 0x100001508, symSize: 0xE0 }
  - { offset: 0x98DBB, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateCACycfcTo', symObjAddr: 0x218, symBinAddr: 0x1000015E8, symSize: 0x48 }
  - { offset: 0x98DF0, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateCfD', symObjAddr: 0x260, symBinAddr: 0x100001630, symSize: 0x30 }
  - { offset: 0x98E1A, size: 0x8, addend: 0x0, symName: '_$sSo29UIApplicationLaunchOptionsKeyaSYSCSY8rawValue03RawF0QzvgTW', symObjAddr: 0x6A0, symBinAddr: 0x100001A70, symSize: 0x28 }
  - { offset: 0x98EA0, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateC5scene_13willConnectTo7optionsySo7UISceneC_So0I7SessionCSo0I17ConnectionOptionsCtFTf4nddn_n', symObjAddr: 0x74C, symBinAddr: 0x100001B1C, symSize: 0x2FC }
  - { offset: 0x990DD, size: 0x8, addend: 0x0, symName: '_$s7MPhotos20MainTabBarControllerC11viewDidLoadyyFTo', symObjAddr: 0x0, symBinAddr: 0x10000216C, symSize: 0x5C }
  - { offset: 0x992A7, size: 0x8, addend: 0x0, symName: '_$s7MPhotos20MainTabBarControllerCMa', symObjAddr: 0x54C, symBinAddr: 0x1000026B8, symSize: 0x20 }
  - { offset: 0x99307, size: 0x8, addend: 0x0, symName: '_$s7MPhotos20MainTabBarControllerC11viewDidLoadyyFTo', symObjAddr: 0x0, symBinAddr: 0x10000216C, symSize: 0x5C }
  - { offset: 0x9957B, size: 0x8, addend: 0x0, symName: '_$s7MPhotos20MainTabBarControllerC20setupViewControllers33_4B23E2A9500355D8539B9FC3D9E272D8LLyyF', symObjAddr: 0x5C, symBinAddr: 0x1000021C8, symSize: 0x210 }
  - { offset: 0x99721, size: 0x8, addend: 0x0, symName: '_$s7MPhotos20MainTabBarControllerC15setupAppearance33_4B23E2A9500355D8539B9FC3D9E272D8LLyyF', symObjAddr: 0x26C, symBinAddr: 0x1000023D8, symSize: 0x108 }
  - { offset: 0x997E1, size: 0x8, addend: 0x0, symName: '_$s7MPhotos20MainTabBarControllerC4tabsACSaySo5UITabCG_tcfcTo', symObjAddr: 0x374, symBinAddr: 0x1000024E0, symSize: 0x98 }
  - { offset: 0x99839, size: 0x8, addend: 0x0, symName: '_$s7MPhotos20MainTabBarControllerC7nibName6bundleACSSSg_So8NSBundleCSgtcfcTo', symObjAddr: 0x40C, symBinAddr: 0x100002578, symSize: 0x9C }
  - { offset: 0x99893, size: 0x8, addend: 0x0, symName: '_$s7MPhotos20MainTabBarControllerC5coderACSgSo7NSCoderC_tcfcTo', symObjAddr: 0x4A8, symBinAddr: 0x100002614, symSize: 0x74 }
  - { offset: 0x998CA, size: 0x8, addend: 0x0, symName: '_$s7MPhotos20MainTabBarControllerCfD', symObjAddr: 0x51C, symBinAddr: 0x100002688, symSize: 0x30 }
  - { offset: 0x9993C, size: 0x8, addend: 0x0, symName: '_$s7MPhotos20MainTabBarControllerC19createPlaceholderVC33_4B23E2A9500355D8539B9FC3D9E272D8LL5titleSo06UIViewE0CSS_tFTf4nd_n', symObjAddr: 0x56C, symBinAddr: 0x1000026D8, symSize: 0x374 }
  - { offset: 0x99ACE, size: 0x8, addend: 0x0, symName: '_$s7MPhotos20MainTabBarControllerC09createNavE033_4B23E2A9500355D8539B9FC3D9E272D8LL6rootVC5title9imageNameSo012UINavigationE0CSo06UIViewE0C_S2StFTf4nnnd_n', symObjAddr: 0x8E0, symBinAddr: 0x100002A4C, symSize: 0x1B8 }
  - { offset: 0x99DE2, size: 0x8, addend: 0x0, symName: '_$s7MPhotos17PermissionManagerC05checkB11OnAppLaunch4from10completionySo16UIViewControllerC_yAC0B6StatusOctF0102$s7MPhotos13SceneDelegateC27checkPhotoLibraryPermission33_2145FA1267F93B6F4563514BDBB8947ELL4fromySo16jk10C_tFyAA0G7c4C0G6L5OcfU_Tf1ncn_n', symObjAddr: 0x0, symBinAddr: 0x100002C44, symSize: 0x458 }
  - { offset: 0x9A1CE, size: 0x8, addend: 0x0, symName: '_$sSo21PHAuthorizationStatusVIegy_ABIeyBy_TR', symObjAddr: 0x6C4, symBinAddr: 0x100003308, symSize: 0x3C }
  - { offset: 0x9A1E6, size: 0x8, addend: 0x0, symName: '_$sSo13UIAlertActionCIegg_ABIeyBy_TR', symObjAddr: 0x900, symBinAddr: 0x100003544, symSize: 0x50 }
  - { offset: 0x9A1FE, size: 0x8, addend: 0x0, symName: '_$s7MPhotos17PermissionManagerCMa', symObjAddr: 0xB6C, symBinAddr: 0x1000037B0, symSize: 0x20 }
  - { offset: 0x9A212, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0xB8C, symBinAddr: 0x1000037D0, symSize: 0x10 }
  - { offset: 0x9A226, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0xB9C, symBinAddr: 0x1000037E0, symSize: 0x8 }
  - { offset: 0x9A23A, size: 0x8, addend: 0x0, symName: '_$s7MPhotos17PermissionManagerC05checkB11OnAppLaunch4from10completionySo16UIViewControllerC_yAC0B6StatusOctFyAJcfU_0102$s7MPhotos13SceneDelegateC27checkPhotoLibraryPermission33_2145FA1267F93B6F4563514BDBB8947ELL4fromySo16jk10C_tFyAA0G7c4C0G6L5OcfU_Tf1nnnc_n', symObjAddr: 0xBA4, symBinAddr: 0x1000037E8, symSize: 0xC0 }
  - { offset: 0x9A27F, size: 0x8, addend: 0x0, symName: '_$s7MPhotos17PermissionManagerC05checkB11OnAppLaunch4from10completionySo16UIViewControllerC_yAC0B6StatusOctFyAJcfU_', symObjAddr: 0xC64, symBinAddr: 0x1000038A8, symSize: 0xD4 }
  - { offset: 0x9A2C3, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4findys10_HashTableV6BucketV6bucket_Sb5foundtxSHRzlFSo38UIApplicationOpenExternalURLOptionsKeya_Tg5', symObjAddr: 0xD38, symBinAddr: 0x10000397C, symSize: 0x80 }
  - { offset: 0x9A362, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4find_9hashValues10_HashTableV6BucketV6bucket_Sb5foundtx_SitSHRzlFSo38UIApplicationOpenExternalURLOptionsKeya_Tg5', symObjAddr: 0xDB8, symBinAddr: 0x1000039FC, symSize: 0x178 }
  - { offset: 0x9A4B5, size: 0x8, addend: 0x0, symName: '_$s10Foundation3URLVSgWOh', symObjAddr: 0x1254, symBinAddr: 0x100003E54, symSize: 0x40 }
  - { offset: 0x9A4C9, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeya_yptWOc', symObjAddr: 0x1294, symBinAddr: 0x100003E94, symSize: 0x48 }
  - { offset: 0x9A4DD, size: 0x8, addend: 0x0, symName: '_$sypWOb', symObjAddr: 0x12DC, symBinAddr: 0x100003EDC, symSize: 0x10 }
  - { offset: 0x9A4F1, size: 0x8, addend: 0x0, symName: '_$s7MPhotos17PermissionManagerC04showB11DeniedAlert4fromySo16UIViewControllerC_tFySo13UIAlertActionCcfU_TA', symObjAddr: 0x12EC, symBinAddr: 0x100003EEC, symSize: 0x4 }
  - { offset: 0x9A509, size: 0x8, addend: 0x0, symName: '_$s7MPhotos17PermissionManagerC04showB11DeniedAlert4fromySo16UIViewControllerC_tFySo13UIAlertActionCcfU_TA', symObjAddr: 0x12EC, symBinAddr: 0x100003EEC, symSize: 0x4 }
  - { offset: 0x9A51D, size: 0x8, addend: 0x0, symName: '_$s7MPhotos17PermissionManagerC04showB11DeniedAlert4fromySo16UIViewControllerC_tFySo13UIAlertActionCcfU_TA', symObjAddr: 0x12EC, symBinAddr: 0x100003EEC, symSize: 0x4 }
  - { offset: 0x9A530, size: 0x8, addend: 0x0, symName: '_$s7MPhotos17PermissionManagerC05checkB11OnAppLaunch4from10completionySo16UIViewControllerC_yAC0B6StatusOctFyAJcfU_TA', symObjAddr: 0x1348, symBinAddr: 0x100003F48, symSize: 0xC }
  - { offset: 0x9A544, size: 0x8, addend: 0x0, symName: '_$s7MPhotos17PermissionManagerC019requestPhotoLibraryB010completionyyAC0B6StatusOc_tFySo015PHAuthorizationH0VcfU_TA', symObjAddr: 0x1380, symBinAddr: 0x100003F80, symSize: 0xC }
  - { offset: 0x9A558, size: 0x8, addend: 0x0, symName: '_$s7MPhotos17PermissionManagerC019requestPhotoLibraryB010completionyyAC0B6StatusOc_tFySo015PHAuthorizationH0VcfU_yyScMYccfU_TA', symObjAddr: 0x13FC, symBinAddr: 0x100003FB8, symSize: 0xC }
  - { offset: 0x9A695, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSo38UIApplicationOpenExternalURLOptionsKeya_ypTt0g5Tf4g_n', symObjAddr: 0xF30, symBinAddr: 0x100003B74, symSize: 0xF0 }
  - { offset: 0x9A936, size: 0x8, addend: 0x0, symName: '_$s7MPhotos17PermissionManagerC019requestPhotoLibraryB010completionyyAC0B6StatusOc_tFySo015PHAuthorizationH0VcfU_', symObjAddr: 0x458, symBinAddr: 0x10000309C, symSize: 0x1E4 }
  - { offset: 0x9A995, size: 0x8, addend: 0x0, symName: '_$s7MPhotos17PermissionManagerC019requestPhotoLibraryB010completionyyAC0B6StatusOc_tFySo015PHAuthorizationH0VcfU_yyScMYccfU_', symObjAddr: 0x63C, symBinAddr: 0x100003280, symSize: 0x88 }
  - { offset: 0x9AA2D, size: 0x8, addend: 0x0, symName: '_$s7MPhotos17PermissionManagerC04showB11DeniedAlert4fromySo16UIViewControllerC_tF', symObjAddr: 0x700, symBinAddr: 0x100003344, symSize: 0x200 }
  - { offset: 0x9AAE8, size: 0x8, addend: 0x0, symName: '_$s7MPhotos17PermissionManagerC22showLimitedAccessAlert4fromySo16UIViewControllerC_tF', symObjAddr: 0x950, symBinAddr: 0x100003594, symSize: 0x20C }
  - { offset: 0x9AB7D, size: 0x8, addend: 0x0, symName: '_$s7MPhotos17PermissionManagerCfD', symObjAddr: 0xB5C, symBinAddr: 0x1000037A0, symSize: 0x10 }
  - { offset: 0x9ABD4, size: 0x8, addend: 0x0, symName: '_$s7MPhotos17PermissionManagerC15openAppSettings33_7A20A667FEBED227F44F8689CCAF6561LLyyFTf4d_n', symObjAddr: 0x1020, symBinAddr: 0x100003C64, symSize: 0x1F0 }
  - { offset: 0x9AD13, size: 0x8, addend: 0x0, symName: '_$s7MPhotos11AppDelegateC11application_29didFinishLaunchingWithOptionsSbSo13UIApplicationC_SDySo0j6LaunchI3KeyaypGSgtFTo', symObjAddr: 0x0, symBinAddr: 0x100003FE0, symSize: 0x8C }
  - { offset: 0x9AE6F, size: 0x8, addend: 0x0, symName: '_$s7MPhotos11AppDelegateCMa', symObjAddr: 0x264, symBinAddr: 0x100004244, symSize: 0x20 }
  - { offset: 0x9AE83, size: 0x8, addend: 0x0, symName: '_$sSo29UIApplicationLaunchOptionsKeyaABSHSCWl', symObjAddr: 0x33C, symBinAddr: 0x10000431C, symSize: 0x44 }
  - { offset: 0x9AE97, size: 0x8, addend: 0x0, symName: '_$sSo15UINavigationBarCMa', symObjAddr: 0x380, symBinAddr: 0x100004360, symSize: 0x44 }
  - { offset: 0x9AFD7, size: 0x8, addend: 0x0, symName: '_$s7MPhotos11AppDelegateC11application_29didFinishLaunchingWithOptionsSbSo13UIApplicationC_SDySo0j6LaunchI3KeyaypGSgtFTo', symObjAddr: 0x0, symBinAddr: 0x100003FE0, symSize: 0x8C }
  - { offset: 0x9B061, size: 0x8, addend: 0x0, symName: '_$s7MPhotos11AppDelegateC11application_26configurationForConnecting7optionsSo20UISceneConfigurationCSo13UIApplicationC_So0I7SessionCSo0I17ConnectionOptionsCtFTo', symObjAddr: 0x8C, symBinAddr: 0x10000406C, symSize: 0xE0 }
  - { offset: 0x9B0FA, size: 0x8, addend: 0x0, symName: '_$s7MPhotos11AppDelegateC11application_23didDiscardSceneSessionsySo13UIApplicationC_ShySo14UISceneSessionCGtFTo', symObjAddr: 0x16C, symBinAddr: 0x10000414C, symSize: 0x4 }
  - { offset: 0x9B122, size: 0x8, addend: 0x0, symName: '_$s7MPhotos11AppDelegateCACycfcTo', symObjAddr: 0x170, symBinAddr: 0x100004150, symSize: 0x3C }
  - { offset: 0x9B157, size: 0x8, addend: 0x0, symName: '_$s7MPhotos11AppDelegateCfD', symObjAddr: 0x1AC, symBinAddr: 0x10000418C, symSize: 0x30 }
  - { offset: 0x9B1A1, size: 0x8, addend: 0x0, symName: _main, symObjAddr: 0x1DC, symBinAddr: 0x1000041BC, symSize: 0x88 }
  - { offset: 0x9B200, size: 0x8, addend: 0x0, symName: '_$s7MPhotos11AppDelegateC11application_29didFinishLaunchingWithOptionsSbSo13UIApplicationC_SDySo0j6LaunchI3KeyaypGSgtFTf4ddd_n', symObjAddr: 0x284, symBinAddr: 0x100004264, symSize: 0xB8 }
  - { offset: 0x9B342, size: 0x8, addend: 0x0, symName: '_$s7MPhotos19ResourceBundleClass33_E00857F5A182E2036586C1283CBC8150LLCMa', symObjAddr: 0x10, symBinAddr: 0x1000043A4, symSize: 0x20 }
...
