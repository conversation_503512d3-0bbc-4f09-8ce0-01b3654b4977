---
triple:          'arm64-apple-darwin'
binary-path:     "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/MPhotos"
relocations:
  - { offset: 0x7A987, size: 0x8, addend: 0x0, symName: '_$s7MPhotos11AppDelegateCMa', symObjAddr: 0x274, symBinAddr: 0x10000143C, symSize: 0x20 }
  - { offset: 0x7AA42, size: 0x8, addend: 0x0, symName: '_$sSo29UIApplicationLaunchOptionsKeyaMa', symObjAddr: 0x704, symBinAddr: 0x1000018CC, symSize: 0x50 }
  - { offset: 0x7AA56, size: 0x8, addend: 0x0, symName: '_$sSo15UINavigationBarCMa', symObjAddr: 0x754, symBinAddr: 0x10000191C, symSize: 0x44 }
  - { offset: 0x7AA6A, size: 0x8, addend: 0x0, symName: '_$sSo29UIApplicationLaunchOptionsKeyas20_SwiftNewtypeWrapperSCSYWb', symObjAddr: 0x798, symBinAddr: 0x100001960, symSize: 0x24 }
  - { offset: 0x7AA7E, size: 0x8, addend: 0x0, symName: '_$sSo29UIApplicationLaunchOptionsKeyas20_SwiftNewtypeWrapperSCs35_HasCustomAnyHashableRepresentationPWb', symObjAddr: 0x7BC, symBinAddr: 0x100001984, symSize: 0x24 }
  - { offset: 0x7AA92, size: 0x8, addend: 0x0, symName: '_$sSo29UIApplicationLaunchOptionsKeyaSHSCSQWb', symObjAddr: 0x7E0, symBinAddr: 0x1000019A8, symSize: 0x24 }
  - { offset: 0x7AAF9, size: 0x8, addend: 0x0, symName: '_$sSo29UIApplicationLaunchOptionsKeyaSHSCSH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x2A0, symBinAddr: 0x100001468, symSize: 0x40 }
  - { offset: 0x7AB7D, size: 0x8, addend: 0x0, symName: '_$sSo29UIApplicationLaunchOptionsKeyaSHSCSH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x2E0, symBinAddr: 0x1000014A8, symSize: 0x70 }
  - { offset: 0x7ABEC, size: 0x8, addend: 0x0, symName: '_$sSo29UIApplicationLaunchOptionsKeyas21_ObjectiveCBridgeableSCsACP016_forceBridgeFromE1C_6resulty01_E5CTypeQz_xSgztFZTW', symObjAddr: 0x398, symBinAddr: 0x100001560, symSize: 0x4 }
  - { offset: 0x7AC1A, size: 0x8, addend: 0x0, symName: '_$ss20_SwiftNewtypeWrapperPss21_ObjectiveCBridgeable8RawValueRpzrlE016_forceBridgeFromD1C_6resultyAD_01_D5CTypeQZ_xSgztFZSo29UIApplicationLaunchOptionsKeya_Tt1gq5', symObjAddr: 0x39C, symBinAddr: 0x100001564, symSize: 0x84 }
  - { offset: 0x7ACAD, size: 0x8, addend: 0x0, symName: '_$sSo29UIApplicationLaunchOptionsKeyas21_ObjectiveCBridgeableSCsACP024_conditionallyBridgeFromE1C_6resultSb01_E5CTypeQz_xSgztFZTW', symObjAddr: 0x420, symBinAddr: 0x1000015E8, symSize: 0x4 }
  - { offset: 0x7ACC9, size: 0x8, addend: 0x0, symName: '_$ss20_SwiftNewtypeWrapperPss21_ObjectiveCBridgeable8RawValueRpzrlE024_conditionallyBridgeFromD1C_6resultSbAD_01_D5CTypeQZ_xSgztFZSo29UIApplicationLaunchOptionsKeya_Tt1gq5', symObjAddr: 0x424, symBinAddr: 0x1000015EC, symSize: 0x8C }
  - { offset: 0x7AD6B, size: 0x8, addend: 0x0, symName: '_$sSo29UIApplicationLaunchOptionsKeyas21_ObjectiveCBridgeableSCsACP026_unconditionallyBridgeFromE1Cyx01_E5CTypeQzSgFZTW', symObjAddr: 0x4B0, symBinAddr: 0x100001678, symSize: 0x40 }
  - { offset: 0x7ADFA, size: 0x8, addend: 0x0, symName: '_$sSo29UIApplicationLaunchOptionsKeyaSQSCSQ2eeoiySbx_xtFZTW', symObjAddr: 0x4F0, symBinAddr: 0x1000016B8, symSize: 0x84 }
  - { offset: 0x7AEA4, size: 0x8, addend: 0x0, symName: '_$sSo29UIApplicationLaunchOptionsKeyas35_HasCustomAnyHashableRepresentationSCsACP03_tofgH0s0gH0VSgyFTW', symObjAddr: 0x5E0, symBinAddr: 0x1000017A8, symSize: 0x6C }
  - { offset: 0x7B016, size: 0x8, addend: 0x0, symName: '_$s7MPhotos11AppDelegateC11application_29didFinishLaunchingWithOptionsSbSo13UIApplicationC_SDySo0j6LaunchI3KeyaypGSgtFTo', symObjAddr: 0x0, symBinAddr: 0x1000011C8, symSize: 0x9C }
  - { offset: 0x7B0A0, size: 0x8, addend: 0x0, symName: '_$s7MPhotos11AppDelegateC11application_26configurationForConnecting7optionsSo20UISceneConfigurationCSo13UIApplicationC_So0I7SessionCSo0I17ConnectionOptionsCtFTo', symObjAddr: 0x9C, symBinAddr: 0x100001264, symSize: 0xE0 }
  - { offset: 0x7B139, size: 0x8, addend: 0x0, symName: '_$s7MPhotos11AppDelegateC11application_23didDiscardSceneSessionsySo13UIApplicationC_ShySo14UISceneSessionCGtFTo', symObjAddr: 0x17C, symBinAddr: 0x100001344, symSize: 0x4 }
  - { offset: 0x7B161, size: 0x8, addend: 0x0, symName: '_$s7MPhotos11AppDelegateCACycfcTo', symObjAddr: 0x180, symBinAddr: 0x100001348, symSize: 0x3C }
  - { offset: 0x7B196, size: 0x8, addend: 0x0, symName: '_$s7MPhotos11AppDelegateCfD', symObjAddr: 0x1BC, symBinAddr: 0x100001384, symSize: 0x30 }
  - { offset: 0x7B1E0, size: 0x8, addend: 0x0, symName: _main, symObjAddr: 0x1EC, symBinAddr: 0x1000013B4, symSize: 0x88 }
  - { offset: 0x7B237, size: 0x8, addend: 0x0, symName: '_$sSo29UIApplicationLaunchOptionsKeyaSYSCSY8rawValuexSg03RawF0Qz_tcfCTW', symObjAddr: 0x574, symBinAddr: 0x10000173C, symSize: 0x44 }
  - { offset: 0x7B262, size: 0x8, addend: 0x0, symName: '_$sSo29UIApplicationLaunchOptionsKeyaSYSCSY8rawValue03RawF0QzvgTW', symObjAddr: 0x5B8, symBinAddr: 0x100001780, symSize: 0x28 }
  - { offset: 0x7B2BB, size: 0x8, addend: 0x0, symName: '_$s7MPhotos11AppDelegateC11application_29didFinishLaunchingWithOptionsSbSo13UIApplicationC_SDySo0j6LaunchI3KeyaypGSgtFTf4ddd_n', symObjAddr: 0x64C, symBinAddr: 0x100001814, symSize: 0xB8 }
  - { offset: 0x7B3D9, size: 0x8, addend: 0x0, symName: '_$s7MPhotos20MainTabBarControllerC11viewDidLoadyyFTo', symObjAddr: 0x0, symBinAddr: 0x100001A0C, symSize: 0x5C }
  - { offset: 0x7B5A3, size: 0x8, addend: 0x0, symName: '_$s7MPhotos20MainTabBarControllerCMa', symObjAddr: 0x54C, symBinAddr: 0x100001F58, symSize: 0x20 }
  - { offset: 0x7B5E3, size: 0x8, addend: 0x0, symName: ___swift_instantiateConcreteTypeFromMangledName, symObjAddr: 0xA98, symBinAddr: 0x1000024A4, symSize: 0x44 }
  - { offset: 0x7B617, size: 0x8, addend: 0x0, symName: '_$s7MPhotos20MainTabBarControllerC11viewDidLoadyyFTo', symObjAddr: 0x0, symBinAddr: 0x100001A0C, symSize: 0x5C }
  - { offset: 0x7B88B, size: 0x8, addend: 0x0, symName: '_$s7MPhotos20MainTabBarControllerC20setupViewControllers33_4B23E2A9500355D8539B9FC3D9E272D8LLyyF', symObjAddr: 0x5C, symBinAddr: 0x100001A68, symSize: 0x210 }
  - { offset: 0x7BA31, size: 0x8, addend: 0x0, symName: '_$s7MPhotos20MainTabBarControllerC15setupAppearance33_4B23E2A9500355D8539B9FC3D9E272D8LLyyF', symObjAddr: 0x26C, symBinAddr: 0x100001C78, symSize: 0x108 }
  - { offset: 0x7BAF1, size: 0x8, addend: 0x0, symName: '_$s7MPhotos20MainTabBarControllerC4tabsACSaySo5UITabCG_tcfcTo', symObjAddr: 0x374, symBinAddr: 0x100001D80, symSize: 0x98 }
  - { offset: 0x7BB49, size: 0x8, addend: 0x0, symName: '_$s7MPhotos20MainTabBarControllerC7nibName6bundleACSSSg_So8NSBundleCSgtcfcTo', symObjAddr: 0x40C, symBinAddr: 0x100001E18, symSize: 0x9C }
  - { offset: 0x7BBA3, size: 0x8, addend: 0x0, symName: '_$s7MPhotos20MainTabBarControllerC5coderACSgSo7NSCoderC_tcfcTo', symObjAddr: 0x4A8, symBinAddr: 0x100001EB4, symSize: 0x74 }
  - { offset: 0x7BBDA, size: 0x8, addend: 0x0, symName: '_$s7MPhotos20MainTabBarControllerCfD', symObjAddr: 0x51C, symBinAddr: 0x100001F28, symSize: 0x30 }
  - { offset: 0x7BC4C, size: 0x8, addend: 0x0, symName: '_$s7MPhotos20MainTabBarControllerC19createPlaceholderVC33_4B23E2A9500355D8539B9FC3D9E272D8LL5titleSo06UIViewE0CSS_tFTf4nd_n', symObjAddr: 0x56C, symBinAddr: 0x100001F78, symSize: 0x374 }
  - { offset: 0x7BDDE, size: 0x8, addend: 0x0, symName: '_$s7MPhotos20MainTabBarControllerC09createNavE033_4B23E2A9500355D8539B9FC3D9E272D8LL6rootVC5title9imageNameSo012UINavigationE0CSo06UIViewE0C_S2StFTf4nnnd_n', symObjAddr: 0x8E0, symBinAddr: 0x1000022EC, symSize: 0x1B8 }
  - { offset: 0x7BF4A, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateC6windowSo8UIWindowCSgvgTo', symObjAddr: 0x0, symBinAddr: 0x100002528, symSize: 0x10 }
  - { offset: 0x7C0EB, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateCfETo', symObjAddr: 0x14C, symBinAddr: 0x100002674, symSize: 0x10 }
  - { offset: 0x7C11A, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateCMa', symObjAddr: 0x15C, symBinAddr: 0x100002684, symSize: 0x20 }
  - { offset: 0x7C220, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateC6windowSo8UIWindowCSgvgTo', symObjAddr: 0x0, symBinAddr: 0x100002528, symSize: 0x10 }
  - { offset: 0x7C238, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateC6windowSo8UIWindowCSgvgTo', symObjAddr: 0x0, symBinAddr: 0x100002528, symSize: 0x10 }
  - { offset: 0x7C280, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateC6windowSo8UIWindowCSgvsTo', symObjAddr: 0x10, symBinAddr: 0x100002538, symSize: 0x34 }
  - { offset: 0x7C2C2, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateC5scene_13willConnectTo7optionsySo7UISceneC_So0I7SessionCSo0I17ConnectionOptionsCtFTo', symObjAddr: 0x44, symBinAddr: 0x10000256C, symSize: 0x7C }
  - { offset: 0x7C2EC, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateC18sceneDidDisconnectyySo7UISceneCFTo', symObjAddr: 0xC0, symBinAddr: 0x1000025E8, symSize: 0x4 }
  - { offset: 0x7C300, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateC20sceneDidBecomeActiveyySo7UISceneCFTo', symObjAddr: 0xC4, symBinAddr: 0x1000025EC, symSize: 0x4 }
  - { offset: 0x7C314, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateC21sceneWillResignActiveyySo7UISceneCFTo', symObjAddr: 0xC8, symBinAddr: 0x1000025F0, symSize: 0x4 }
  - { offset: 0x7C328, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateC24sceneWillEnterForegroundyySo7UISceneCFTo', symObjAddr: 0xCC, symBinAddr: 0x1000025F4, symSize: 0x4 }
  - { offset: 0x7C33C, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateC23sceneDidEnterBackgroundyySo7UISceneCFTo', symObjAddr: 0xD0, symBinAddr: 0x1000025F8, symSize: 0x4 }
  - { offset: 0x7C364, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateCACycfcTo', symObjAddr: 0xD4, symBinAddr: 0x1000025FC, symSize: 0x48 }
  - { offset: 0x7C399, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateCfD', symObjAddr: 0x11C, symBinAddr: 0x100002644, symSize: 0x30 }
  - { offset: 0x7C3E4, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateC5scene_13willConnectTo7optionsySo7UISceneC_So0I7SessionCSo0I17ConnectionOptionsCtFTf4nddn_n', symObjAddr: 0x17C, symBinAddr: 0x1000026A4, symSize: 0x11C }
  - { offset: 0x7C59A, size: 0x8, addend: 0x0, symName: '_$s7MPhotos19ResourceBundleClass33_E00857F5A182E2036586C1283CBC8150LLCfD', symObjAddr: 0x0, symBinAddr: 0x1000027C0, symSize: 0x10 }
  - { offset: 0x7C5DC, size: 0x8, addend: 0x0, symName: '_$s7MPhotos19ResourceBundleClass33_E00857F5A182E2036586C1283CBC8150LLCMa', symObjAddr: 0x10, symBinAddr: 0x1000027D0, symSize: 0x20 }
  - { offset: 0x7C5F7, size: 0x8, addend: 0x0, symName: '_$s7MPhotos19ResourceBundleClass33_E00857F5A182E2036586C1283CBC8150LLCfD', symObjAddr: 0x0, symBinAddr: 0x1000027C0, symSize: 0x10 }
...
