---
triple:          'arm64-apple-darwin'
binary-path:     "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/MPhotos"
relocations:
  - { offset: 0x7A9C8, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateCfETo', symObjAddr: 0x14C, symBinAddr: 0x100001314, symSize: 0x10 }
  - { offset: 0x7A9F7, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateCMa', symObjAddr: 0x15C, symBinAddr: 0x100001324, symSize: 0x20 }
  - { offset: 0x7AAB2, size: 0x8, addend: 0x0, symName: '_$sSo29UIApplicationLaunchOptionsKeyaMa', symObjAddr: 0x650, symBinAddr: 0x100001818, symSize: 0x50 }
  - { offset: 0x7AAC6, size: 0x8, addend: 0x0, symName: '_$sSo29UIApplicationLaunchOptionsKeyas20_SwiftNewtypeWrapperSCSYWb', symObjAddr: 0x6A0, symBinAddr: 0x100001868, symSize: 0x24 }
  - { offset: 0x7AADA, size: 0x8, addend: 0x0, symName: '_$sSo29UIApplicationLaunchOptionsKeyas20_SwiftNewtypeWrapperSCs35_HasCustomAnyHashableRepresentationPWb', symObjAddr: 0x6C4, symBinAddr: 0x10000188C, symSize: 0x24 }
  - { offset: 0x7AAEE, size: 0x8, addend: 0x0, symName: '_$sSo29UIApplicationLaunchOptionsKeyaSHSCSQWb', symObjAddr: 0x6E8, symBinAddr: 0x1000018B0, symSize: 0x24 }
  - { offset: 0x7AB42, size: 0x8, addend: 0x0, symName: '_$sSo29UIApplicationLaunchOptionsKeyaSHSCSH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x188, symBinAddr: 0x100001350, symSize: 0x40 }
  - { offset: 0x7ABC6, size: 0x8, addend: 0x0, symName: '_$sSo29UIApplicationLaunchOptionsKeyaSHSCSH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x1C8, symBinAddr: 0x100001390, symSize: 0x70 }
  - { offset: 0x7AC35, size: 0x8, addend: 0x0, symName: '_$sSo29UIApplicationLaunchOptionsKeyas21_ObjectiveCBridgeableSCsACP016_forceBridgeFromE1C_6resulty01_E5CTypeQz_xSgztFZTW', symObjAddr: 0x280, symBinAddr: 0x100001448, symSize: 0x4 }
  - { offset: 0x7AC63, size: 0x8, addend: 0x0, symName: '_$ss20_SwiftNewtypeWrapperPss21_ObjectiveCBridgeable8RawValueRpzrlE016_forceBridgeFromD1C_6resultyAD_01_D5CTypeQZ_xSgztFZSo29UIApplicationLaunchOptionsKeya_Tt1gq5', symObjAddr: 0x284, symBinAddr: 0x10000144C, symSize: 0x84 }
  - { offset: 0x7ACF6, size: 0x8, addend: 0x0, symName: '_$sSo29UIApplicationLaunchOptionsKeyas21_ObjectiveCBridgeableSCsACP024_conditionallyBridgeFromE1C_6resultSb01_E5CTypeQz_xSgztFZTW', symObjAddr: 0x308, symBinAddr: 0x1000014D0, symSize: 0x4 }
  - { offset: 0x7AD18, size: 0x8, addend: 0x0, symName: '_$ss20_SwiftNewtypeWrapperPss21_ObjectiveCBridgeable8RawValueRpzrlE024_conditionallyBridgeFromD1C_6resultSbAD_01_D5CTypeQZ_xSgztFZSo29UIApplicationLaunchOptionsKeya_Tt1gq5', symObjAddr: 0x30C, symBinAddr: 0x1000014D4, symSize: 0x8C }
  - { offset: 0x7ADBA, size: 0x8, addend: 0x0, symName: '_$sSo29UIApplicationLaunchOptionsKeyas21_ObjectiveCBridgeableSCsACP026_unconditionallyBridgeFromE1Cyx01_E5CTypeQzSgFZTW', symObjAddr: 0x398, symBinAddr: 0x100001560, symSize: 0x40 }
  - { offset: 0x7AE49, size: 0x8, addend: 0x0, symName: '_$sSo29UIApplicationLaunchOptionsKeyaSQSCSQ2eeoiySbx_xtFZTW', symObjAddr: 0x3D8, symBinAddr: 0x1000015A0, symSize: 0x84 }
  - { offset: 0x7AEF3, size: 0x8, addend: 0x0, symName: '_$sSo29UIApplicationLaunchOptionsKeyas35_HasCustomAnyHashableRepresentationSCsACP03_tofgH0s0gH0VSgyFTW', symObjAddr: 0x4C8, symBinAddr: 0x100001690, symSize: 0x6C }
  - { offset: 0x7B05F, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateC6windowSo8UIWindowCSgvgTo', symObjAddr: 0x0, symBinAddr: 0x1000011C8, symSize: 0x10 }
  - { offset: 0x7B077, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateC6windowSo8UIWindowCSgvgTo', symObjAddr: 0x0, symBinAddr: 0x1000011C8, symSize: 0x10 }
  - { offset: 0x7B0BF, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateC6windowSo8UIWindowCSgvsTo', symObjAddr: 0x10, symBinAddr: 0x1000011D8, symSize: 0x34 }
  - { offset: 0x7B101, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateC5scene_13willConnectTo7optionsySo7UISceneC_So0I7SessionCSo0I17ConnectionOptionsCtFTo', symObjAddr: 0x44, symBinAddr: 0x10000120C, symSize: 0x7C }
  - { offset: 0x7B12B, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateC18sceneDidDisconnectyySo7UISceneCFTo', symObjAddr: 0xC0, symBinAddr: 0x100001288, symSize: 0x4 }
  - { offset: 0x7B13F, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateC20sceneDidBecomeActiveyySo7UISceneCFTo', symObjAddr: 0xC4, symBinAddr: 0x10000128C, symSize: 0x4 }
  - { offset: 0x7B153, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateC21sceneWillResignActiveyySo7UISceneCFTo', symObjAddr: 0xC8, symBinAddr: 0x100001290, symSize: 0x4 }
  - { offset: 0x7B167, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateC24sceneWillEnterForegroundyySo7UISceneCFTo', symObjAddr: 0xCC, symBinAddr: 0x100001294, symSize: 0x4 }
  - { offset: 0x7B17B, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateC23sceneDidEnterBackgroundyySo7UISceneCFTo', symObjAddr: 0xD0, symBinAddr: 0x100001298, symSize: 0x4 }
  - { offset: 0x7B1A3, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateCACycfcTo', symObjAddr: 0xD4, symBinAddr: 0x10000129C, symSize: 0x48 }
  - { offset: 0x7B1D8, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateCfD', symObjAddr: 0x11C, symBinAddr: 0x1000012E4, symSize: 0x30 }
  - { offset: 0x7B223, size: 0x8, addend: 0x0, symName: '_$sSo29UIApplicationLaunchOptionsKeyaSYSCSY8rawValuexSg03RawF0Qz_tcfCTW', symObjAddr: 0x45C, symBinAddr: 0x100001624, symSize: 0x44 }
  - { offset: 0x7B24E, size: 0x8, addend: 0x0, symName: '_$sSo29UIApplicationLaunchOptionsKeyaSYSCSY8rawValue03RawF0QzvgTW', symObjAddr: 0x4A0, symBinAddr: 0x100001668, symSize: 0x28 }
  - { offset: 0x7B29F, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateC5scene_13willConnectTo7optionsySo7UISceneC_So0I7SessionCSo0I17ConnectionOptionsCtFTf4nddn_n', symObjAddr: 0x534, symBinAddr: 0x1000016FC, symSize: 0x11C }
  - { offset: 0x7B469, size: 0x8, addend: 0x0, symName: '_$s7MPhotos20MainTabBarControllerC11viewDidLoadyyFTo', symObjAddr: 0x0, symBinAddr: 0x100001914, symSize: 0x5C }
  - { offset: 0x7B633, size: 0x8, addend: 0x0, symName: '_$s7MPhotos20MainTabBarControllerCMa', symObjAddr: 0x54C, symBinAddr: 0x100001E60, symSize: 0x20 }
  - { offset: 0x7B673, size: 0x8, addend: 0x0, symName: ___swift_instantiateConcreteTypeFromMangledName, symObjAddr: 0xA98, symBinAddr: 0x1000023AC, symSize: 0x44 }
  - { offset: 0x7B6A7, size: 0x8, addend: 0x0, symName: '_$s7MPhotos20MainTabBarControllerC11viewDidLoadyyFTo', symObjAddr: 0x0, symBinAddr: 0x100001914, symSize: 0x5C }
  - { offset: 0x7B91B, size: 0x8, addend: 0x0, symName: '_$s7MPhotos20MainTabBarControllerC20setupViewControllers33_4B23E2A9500355D8539B9FC3D9E272D8LLyyF', symObjAddr: 0x5C, symBinAddr: 0x100001970, symSize: 0x210 }
  - { offset: 0x7BAC1, size: 0x8, addend: 0x0, symName: '_$s7MPhotos20MainTabBarControllerC15setupAppearance33_4B23E2A9500355D8539B9FC3D9E272D8LLyyF', symObjAddr: 0x26C, symBinAddr: 0x100001B80, symSize: 0x108 }
  - { offset: 0x7BB81, size: 0x8, addend: 0x0, symName: '_$s7MPhotos20MainTabBarControllerC4tabsACSaySo5UITabCG_tcfcTo', symObjAddr: 0x374, symBinAddr: 0x100001C88, symSize: 0x98 }
  - { offset: 0x7BBD9, size: 0x8, addend: 0x0, symName: '_$s7MPhotos20MainTabBarControllerC7nibName6bundleACSSSg_So8NSBundleCSgtcfcTo', symObjAddr: 0x40C, symBinAddr: 0x100001D20, symSize: 0x9C }
  - { offset: 0x7BC33, size: 0x8, addend: 0x0, symName: '_$s7MPhotos20MainTabBarControllerC5coderACSgSo7NSCoderC_tcfcTo', symObjAddr: 0x4A8, symBinAddr: 0x100001DBC, symSize: 0x74 }
  - { offset: 0x7BC6A, size: 0x8, addend: 0x0, symName: '_$s7MPhotos20MainTabBarControllerCfD', symObjAddr: 0x51C, symBinAddr: 0x100001E30, symSize: 0x30 }
  - { offset: 0x7BCDC, size: 0x8, addend: 0x0, symName: '_$s7MPhotos20MainTabBarControllerC19createPlaceholderVC33_4B23E2A9500355D8539B9FC3D9E272D8LL5titleSo06UIViewE0CSS_tFTf4nd_n', symObjAddr: 0x56C, symBinAddr: 0x100001E80, symSize: 0x374 }
  - { offset: 0x7BE6E, size: 0x8, addend: 0x0, symName: '_$s7MPhotos20MainTabBarControllerC09createNavE033_4B23E2A9500355D8539B9FC3D9E272D8LL6rootVC5title9imageNameSo012UINavigationE0CSo06UIViewE0C_S2StFTf4nnnd_n', symObjAddr: 0x8E0, symBinAddr: 0x1000021F4, symSize: 0x1B8 }
  - { offset: 0x7BFDE, size: 0x8, addend: 0x0, symName: '_$s7MPhotos11AppDelegateC11application_29didFinishLaunchingWithOptionsSbSo13UIApplicationC_SDySo0j6LaunchI3KeyaypGSgtFTo', symObjAddr: 0x0, symBinAddr: 0x100002430, symSize: 0x8C }
  - { offset: 0x7C13A, size: 0x8, addend: 0x0, symName: '_$s7MPhotos11AppDelegateCMa', symObjAddr: 0x264, symBinAddr: 0x100002694, symSize: 0x20 }
  - { offset: 0x7C14E, size: 0x8, addend: 0x0, symName: '_$sSo29UIApplicationLaunchOptionsKeyaABSHSCWl', symObjAddr: 0x33C, symBinAddr: 0x10000276C, symSize: 0x44 }
  - { offset: 0x7C162, size: 0x8, addend: 0x0, symName: '_$sSo15UINavigationBarCMa', symObjAddr: 0x380, symBinAddr: 0x1000027B0, symSize: 0x44 }
  - { offset: 0x7C2A2, size: 0x8, addend: 0x0, symName: '_$s7MPhotos11AppDelegateC11application_29didFinishLaunchingWithOptionsSbSo13UIApplicationC_SDySo0j6LaunchI3KeyaypGSgtFTo', symObjAddr: 0x0, symBinAddr: 0x100002430, symSize: 0x8C }
  - { offset: 0x7C32C, size: 0x8, addend: 0x0, symName: '_$s7MPhotos11AppDelegateC11application_26configurationForConnecting7optionsSo20UISceneConfigurationCSo13UIApplicationC_So0I7SessionCSo0I17ConnectionOptionsCtFTo', symObjAddr: 0x8C, symBinAddr: 0x1000024BC, symSize: 0xE0 }
  - { offset: 0x7C3C5, size: 0x8, addend: 0x0, symName: '_$s7MPhotos11AppDelegateC11application_23didDiscardSceneSessionsySo13UIApplicationC_ShySo14UISceneSessionCGtFTo', symObjAddr: 0x16C, symBinAddr: 0x10000259C, symSize: 0x4 }
  - { offset: 0x7C3ED, size: 0x8, addend: 0x0, symName: '_$s7MPhotos11AppDelegateCACycfcTo', symObjAddr: 0x170, symBinAddr: 0x1000025A0, symSize: 0x3C }
  - { offset: 0x7C422, size: 0x8, addend: 0x0, symName: '_$s7MPhotos11AppDelegateCfD', symObjAddr: 0x1AC, symBinAddr: 0x1000025DC, symSize: 0x30 }
  - { offset: 0x7C46C, size: 0x8, addend: 0x0, symName: _main, symObjAddr: 0x1DC, symBinAddr: 0x10000260C, symSize: 0x88 }
  - { offset: 0x7C4CB, size: 0x8, addend: 0x0, symName: '_$s7MPhotos11AppDelegateC11application_29didFinishLaunchingWithOptionsSbSo13UIApplicationC_SDySo0j6LaunchI3KeyaypGSgtFTf4ddd_n', symObjAddr: 0x284, symBinAddr: 0x1000026B4, symSize: 0xB8 }
  - { offset: 0x7C5D5, size: 0x8, addend: 0x0, symName: '_$s7MPhotos19ResourceBundleClass33_E00857F5A182E2036586C1283CBC8150LLCfD', symObjAddr: 0x0, symBinAddr: 0x1000027F4, symSize: 0x10 }
  - { offset: 0x7C617, size: 0x8, addend: 0x0, symName: '_$s7MPhotos19ResourceBundleClass33_E00857F5A182E2036586C1283CBC8150LLCMa', symObjAddr: 0x10, symBinAddr: 0x100002804, symSize: 0x20 }
  - { offset: 0x7C632, size: 0x8, addend: 0x0, symName: '_$s7MPhotos19ResourceBundleClass33_E00857F5A182E2036586C1283CBC8150LLCfD', symObjAddr: 0x0, symBinAddr: 0x1000027F4, symSize: 0x10 }
...
