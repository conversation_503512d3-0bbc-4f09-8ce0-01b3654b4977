version: "Apple Swift version 6.1.2 (swiftlang-*******.2 clang-1700.0.13.5)"
options: "4e9319e3b897ae475d512cf4ca833d3dafb1641c49643b6d281a9d979da8bead"
build_start_time: [1754320880, 342547000]
build_end_time: [1754320881, 460467000]
inputs:
  ? "/Users/<USER>/Desktop/\u7EAF\u6587\u6863\u9A71\u52A8\u7248/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/DerivedSources/GeneratedAssetSymbols.swift"
  : [1754318840, 758933370]
  "/Users/<USER>/Desktop/\u7EAF\u6587\u6863\u9A71\u52A8\u7248/MPhotos/MPhotos/App/MainTabBarController.swift": [1754306217, 100807624]
  "/Users/<USER>/Desktop/\u7EAF\u6587\u6863\u9A71\u52A8\u7248/MPhotos/MPhotos/AppDelegate.swift": [1754306181, 504064780]
  "/Users/<USER>/Desktop/\u7EAF\u6587\u6863\u9A71\u52A8\u7248/MPhotos/MPhotos/SceneDelegate.swift": [1754320837, 724744512]
  "/Users/<USER>/Desktop/\u7EAF\u6587\u6863\u9A71\u52A8\u7248/MPhotos/MPhotos/Utilities/PermissionManager.swift": [1754320804, 835260524]
