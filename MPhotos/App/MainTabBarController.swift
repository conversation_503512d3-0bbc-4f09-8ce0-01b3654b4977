import UIKit

class MainTabBarController: UITabBarController {
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupViewControllers()
        setupAppearance()
    }
    
    private func setupViewControllers() {
        // 图库
        let photoGridVC = createNavController(
            rootVC: PhotoGridTestViewController(),
            title: "图库",
            imageName: "photo.on.rectangle"
        )
        
        // 整理
        let cleanupVC = createNavController(
            rootVC: createPlaceholderVC(title: "整理"),
            title: "整理",
            imageName: "sparkles"
        )
        
        // 相簿
        let albumsVC = createNavController(
            rootVC: createPlaceholderVC(title: "相簿"),
            title: "相簿",
            imageName: "rectangle.stack"
        )
        
        // 功能
        let toolsVC = createNavController(
            rootVC: createPlaceholderVC(title: "功能"),
            title: "功能",
            imageName: "ellipsis.circle"
        )
        
        viewControllers = [photoGridVC, cleanupVC, albumsVC, toolsVC]
    }
    
    private func createNavController(rootVC: UIViewController, title: String, imageName: String) -> UINavigationController {
        let navController = UINavigationController(rootViewController: rootVC)
        navController.tabBarItem.title = title
        navController.tabBarItem.image = UIImage(systemName: imageName)
        navController.navigationBar.prefersLargeTitles = true
        rootVC.navigationItem.title = title
        return navController
    }
    
    private func createPlaceholderVC(title: String) -> UIViewController {
        let vc = UIViewController()
        vc.view.backgroundColor = .systemBackground
        
        // 添加占位标签
        let label = UILabel()
        label.text = "\(title)功能开发中..."
        label.textAlignment = .center
        label.textColor = .secondaryLabel
        label.translatesAutoresizingMaskIntoConstraints = false
        
        vc.view.addSubview(label)
        NSLayoutConstraint.activate([
            label.centerXAnchor.constraint(equalTo: vc.view.centerXAnchor),
            label.centerYAnchor.constraint(equalTo: vc.view.centerYAnchor)
        ])
        
        return vc
    }
    
    private func setupAppearance() {
        // Tab Bar 外观设置
        tabBar.tintColor = .systemBlue
        
        if #available(iOS 15.0, *) {
            let appearance = UITabBarAppearance()
            appearance.configureWithDefaultBackground()
            tabBar.standardAppearance = appearance
            tabBar.scrollEdgeAppearance = appearance
        }
    }
}
